<template>
  <div class="unfinished">
    <div class="unfinished-bg common-bg text-left">
      <div class="unfinished-title common-title padding-left-xll">久拖不决</div>
    </div>
    <div class="unfinished-content">
      <div class="unfinished-left">
        <div class="unfinished-left-value">{{ totality }}</div>
      </div>
      <div class="unfinished-right">
        <div class="unfinished-right-title">
          <div class="unfinished-right-title-text">超</div>
          <div class="unfinished-right-title-actions">
            <div class="unfinished-right-title-action" :class="{ active: selectedDays === 15 }"
              @click="handleClick(15)">15</div>
            <div class="unfinished-right-title-action" :class="{ active: selectedDays === 30 }"
              @click="handleClick(30)">30</div>
            <div class="unfinished-right-title-action" :class="{ active: selectedDays === 60 }"
              @click="handleClick(60)">60</div>
          </div>
          <div class="unfinished-right-title-text">天</div>
        </div>
        <div class="unfinished-list">
          <div class="unfinished-list-item" v-for="item in listData">
            <div class="unfinished-list-item-title">{{ item.name }}</div>
            <div class="unfinished-list-item-content">{{ item.value }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref } from 'vue';
const selectedDays = ref(15);
const totality = ref(4293);

const listData = ref([
  {
    name: '浑南区',
    value: 85
  }, {
    name: '浑南区',
    value: 85
  }, {
    name: '浑南区',
    value: 85
  }
])

const handleClick = (days) => {
  console.log(days);

  selectedDays.value = days;
};

</script>
<style scoped>
.unfinished {
  width: 738px;
}

.unfinished-bg {
  background-image: url('../../src/assets/img/huawu.png');
  background-size: 100% 41px;
  height: 41px;
  margin-bottom: 11px;
}

.unfinished-title {
  height: 21px;
  padding-left: 97px;
  padding-bottom: 10px;
}

.unfinished-content {
  display: flex;
}

.unfinished-left {
  width: 254px;
  height: 301px;
  margin-left: 31px;
  background-image: url('../../src/assets/img/unfinished.png');
}

.unfinished-left-value {
  padding-top: calc(199px - (36px - 26px));
  font-family: YouSheBiaoTiHei;
  font-weight: 400;
  font-size: 36px;
  line-height: 36px;
  color: #FFFFFF;
  text-shadow: 0px 4px 5px rgba(13, 4, 8, 0.2);
  background: linear-gradient(180deg, #8ED2FF 0%, #FFFFFF 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.unfinished-right {
  margin-left: 9px;
}

.unfinished-right-title {
  height: 36px;
  width: 435px;
  background-image: url('../../src/assets/img/footer-title.png');
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  padding-bottom: 10px;
}

.unfinished-right-title-actions {
  display: flex;
  gap: 8px;
}

.unfinished-right-title-action {
  width: 32px;
  height: 24px;
  background-image: url('../../src/assets/img/switch.png');

  font-family: PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #6C9CE3;
  line-height: 24px;
  cursor: pointer;
}

.unfinished-right-title-action.active {
  color: #E3AE3B;
  background-image: url('../../src/assets/img/switch-select.png');
}

.unfinished-list {}

.unfinished-list-item {
  width: 434px;
  height: 68px;
  background-image: url('../../src/assets/img/unfinished-list-bg.png');
  display: flex;
  line-height: 68px;
  position: relative;
  margin-bottom: 25px;
}

.unfinished-list-item:last-child {
  margin-bottom: 0;
}

.unfinished-list-item-title {
  font-family: Microsoft YaHei;
  font-weight: bold;
  font-size: 15px;
  color: #FEFEFE;
  position: absolute;
  left: 142px;
}

.unfinished-list-item-content {
  font-family: Microsoft YaHei;
  font-weight: bold;
  font-size: 24px;
  color: #C5983A;
  text-shadow: 0px 4px 5px rgba(13, 4, 8, 0.2);
  position: absolute;
  left: 238px;
  color: #00FECB;
  text-shadow: 0px 4px 5px rgba(13, 4, 8, 0.2);
  background: linear-gradient(180deg, #C5983A 0%, #FFFFFF 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}
</style>
